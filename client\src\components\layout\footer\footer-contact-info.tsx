import { useLanguage } from '@/hooks/use-language';
import { MapPin, Phone, Mail, Clock } from 'lucide-react';

export function FooterContactInfo() {
  const { t } = useLanguage();

  return (
    <div>
      <h4 className="font-playfair text-lg font-semibold mb-6">{t('footer.contactInfo')}</h4>
      <div className="space-y-4">
        <div className="flex items-start space-x-3">
          <MapPin className="text-gold mt-1 h-5 w-5" />
          <div>
            <p className="text-gray-300 whitespace-pre-line">{t('footer.address')}</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Phone className="text-gold h-5 w-5" />
          <a
            href={`tel:${t('footer.phone')}`}
            className="text-gray-300 hover:text-gold transition-colors duration-300">
            {t('footer.phone')}
          </a>
        </div>
        <div className="flex items-center space-x-3">
          <Mail className="text-gold h-5 w-5" />
          <a
            href={`mailto:${t('footer.email')}`}
            className="text-gray-300 hover:text-gold transition-colors duration-300">
            {t('footer.email')}
          </a>
        </div>
        <div className="flex items-center space-x-3">
          <Clock className="text-gold h-5 w-5" />
          <span className="text-gray-300">{t('footer.hours')}</span>
        </div>
      </div>
    </div>
  );
}

{"hash": "e1ef8dfe", "configHash": "65a53d77", "lockfileHash": "dbeb8c1a", "browserHash": "493dd035", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "f1d4538d", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "9618e1aa", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "75b06668", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "acc2f72f", "needsInterop": true}, "@hookform/resolvers/zod": {"src": "../../@hookform/resolvers/zod/dist/zod.mjs", "file": "@hookform_resolvers_zod.js", "fileHash": "b5a3b244", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "e946b17b", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "cf29032e", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "70defde3", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "0509a1b0", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "34da4c04", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "6435712b", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "08e4e0e1", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "833edb54", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "<PERSON><PERSON><PERSON><PERSON>", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "8a7092cf", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "c7378d9c", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "b5eec7c8", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "bebb127f", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "623c9dcd", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "6aad30f7", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "c0670a76", "needsInterop": true}, "react-hook-form": {"src": "../../react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "a9be6f99", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "c9ac2059", "needsInterop": false}, "wouter": {"src": "../../wouter/esm/index.js", "file": "wouter.js", "fileHash": "10fa8bec", "needsInterop": false}, "zod": {"src": "../../zod/index.js", "file": "zod.js", "fileHash": "7bee25c4", "needsInterop": false}}, "chunks": {"chunk-E45LNNVT": {"file": "chunk-E45LNNVT.js"}, "chunk-EXX3MRKG": {"file": "chunk-EXX3MRKG.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-QH7Y6EI7": {"file": "chunk-QH7Y6EI7.js"}, "chunk-4LGDXRL5": {"file": "chunk-4LGDXRL5.js"}, "chunk-HGJTK426": {"file": "chunk-HGJTK426.js"}, "chunk-TH62OU6K": {"file": "chunk-TH62OU6K.js"}, "chunk-AQU2EBRZ": {"file": "chunk-AQU2EBRZ.js"}, "chunk-A3FISX7A": {"file": "chunk-A3FISX7A.js"}, "chunk-NFCKRDSA": {"file": "chunk-NFCKRDSA.js"}, "chunk-MMBKCDH3": {"file": "chunk-MMBKCDH3.js"}, "chunk-2V3IRYIZ": {"file": "chunk-2V3IRYIZ.js"}, "chunk-RJLARJ2F": {"file": "chunk-RJLARJ2F.js"}, "chunk-2MHJNVUX": {"file": "chunk-2MHJNVUX.js"}, "chunk-VMKDFUY6": {"file": "chunk-VMKDFUY6.js"}, "chunk-YD5KNE6W": {"file": "chunk-YD5KNE6W.js"}, "chunk-JFWPD3MN": {"file": "chunk-JFWPD3MN.js"}, "chunk-WERSD76P": {"file": "chunk-WERSD76P.js"}, "chunk-S77I6LSE": {"file": "chunk-S77I6LSE.js"}, "chunk-3TFVT2CW": {"file": "chunk-3TFVT2CW.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}
import { useLanguage } from '@/hooks/use-language';
import { <PERSON> } from 'wouter';
import { Gem, MapPin, Phone, Mail, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';

export function Footer() {
  const { t, isRTL } = useLanguage();

  const socialLinks = [
    { icon: 'facebook', href: '#' },
    { icon: 'instagram', href: '#' },
    { icon: 'linkedin', href: '#' },
    { icon: 'youtube', href: '#' },
  ];

  const quickLinks = [
    { href: '/', label: t('nav.home') },
    { href: '/products', label: t('nav.products') },
    { href: '/categories', label: t('nav.categories') },
    { href: '/about', label: t('nav.about') },
    { href: '/contact', label: t('nav.contact') },
  ];

  return (
    <footer className="bg-charcoal text-white pt-16 pb-8">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-4 mb-6">
              <div className="w-12 h-12 gold-gradient rounded-lg flex items-center justify-center">
                <Gem className="text-white text-xl" />
              </div>
              <div>
                <h3 className="font-playfair font-bold text-xl">{t('footer.companyName')}</h3>
                <p className="text-gray-300 text-sm">{t('footer.companyTagline')}</p>
              </div>
            </div>
            <p className="text-gray-300 leading-relaxed mb-6 max-w-md">{t('footer.description')}</p>
            <div className="flex space-x-4">
              {socialLinks.map(social => (
                <Button
                  key={social.icon}
                  variant="ghost"
                  size="sm"
                  className="w-10 h-10 bg-gray-700 rounded-lg hover:bg-gold transition-colors duration-300"
                  asChild>
                  <a href={social.href} target="_blank" rel="noopener noreferrer">
                    <i className={`fab fa-${social.icon}`} />
                  </a>
                </Button>
              ))}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="font-playfair text-lg font-semibold mb-6">{t('footer.quickLinks')}</h4>
            <ul className="space-y-3">
              {quickLinks.map(link => (
                <li key={link.href}>
                  <Link href={link.href} className="text-gray-300 hover:text-gold transition-colors duration-300">
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="font-playfair text-lg font-semibold mb-6">{t('footer.contactInfo')}</h4>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <MapPin className="text-gold mt-1 h-5 w-5" />
                <div>
                  <p className="text-gray-300 whitespace-pre-line">{t('footer.address')}</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="text-gold h-5 w-5" />
                <a
                  href={`tel:${t('footer.phone')}`}
                  className="text-gray-300 hover:text-gold transition-colors duration-300">
                  {t('footer.phone')}
                </a>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="text-gold h-5 w-5" />
                <a
                  href={`mailto:${t('footer.email')}`}
                  className="text-gray-300 hover:text-gold transition-colors duration-300">
                  {t('footer.email')}
                </a>
              </div>
              <div className="flex items-center space-x-3">
                <Clock className="text-gold h-5 w-5" />
                <span className="text-gray-300">{t('footer.hours')}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-700 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">{t('footer.copyright')}</p>
            <div className="flex items-center space-x-6 mt-4 md:mt-0">
              <Link href="/privacy" className="text-gray-400 hover:text-gold text-sm transition-colors duration-300">
                {t('footer.privacyPolicy')}
              </Link>
              <Link href="/terms" className="text-gray-400 hover:text-gold text-sm transition-colors duration-300">
                {t('footer.termsOfService')}
              </Link>
              <Link href="/cookies" className="text-gray-400 hover:text-gold text-sm transition-colors duration-300">
                {t('footer.cookiePolicy')}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}

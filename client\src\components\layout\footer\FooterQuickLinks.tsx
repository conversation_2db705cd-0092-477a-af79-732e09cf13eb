import { useLanguage } from '@/hooks/use-language';
import { Link } from 'wouter';

interface QuickLink {
  href: string;
  label: string;
}

export function FooterQuickLinks() {
  const { t } = useLanguage();

  const quickLinks: QuickLink[] = [
    { href: '/', label: t('nav.home') },
    { href: '/products', label: t('nav.products') },
    { href: '/categories', label: t('nav.categories') },
    { href: '/about', label: t('nav.about') },
    { href: '/contact', label: t('nav.contact') },
  ];

  return (
    <div>
      <h4 className="font-playfair text-lg font-semibold mb-6">{t('footer.quickLinks')}</h4>
      <ul className="space-y-3">
        {quickLinks.map(link => (
          <li key={link.href}>
            <Link href={link.href} className="text-gray-300 hover:text-gold transition-colors duration-300">
              {link.label}
            </Link>
          </li>
        ))}
      </ul>
    </div>
  );
}

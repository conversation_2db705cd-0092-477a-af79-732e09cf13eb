# ===== ENVIRONMENT & SECRETS =====
# Environment variables with sensitive data
.env
.env.local
.env.production
.env.staging

# API keys and secrets
*.key
*.pem
secrets/
config/secrets.json

# ===== NODE.JS & NPM =====
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Package manager lock files (keep package-lock.json for consistency)
# yarn.lock
# pnpm-lock.yaml

# ===== BUILD ARTIFACTS =====
# Production builds
dist/
build/
out/

# Vite build cache
.vite/
vite.config.js.timestamp-*

# TypeScript build info
*.tsbuildinfo

# ===== DEVELOPMENT TOOLS =====
# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# ===== LOGS =====
# Log files
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# ===== UPLOADS & USER CONTENT =====
# File uploads (keep structure but not actual files)
uploads/*
!uploads/.gitkeep
server/uploads/*
!server/uploads/.gitkeep

# User generated content
user-uploads/
temp/
tmp/

# ===== CACHE & TEMPORARY FILES =====
# Cache directories
.cache/
.parcel-cache/
.next/
.nuxt/
.vuepress/dist/

# Temporary folders
.tmp/
.temp/

# ===== TESTING =====
# Test coverage
coverage/
*.lcov
.nyc_output/

# Jest
jest.config.js

# ===== DATABASE =====
# Local database files
*.sqlite
*.sqlite3
*.db

# Database dumps
*.sql
*.dump

# ===== DEPLOYMENT =====
# Deployment artifacts
.vercel/
.netlify/
.firebase/

# Docker
.dockerignore
Dockerfile.prod

# ===== MISCELLANEOUS =====
# Backup files
*.bak
*.backup
*.old

# Archive files
*.zip
*.tar.gz
*.rar

# Documentation build
docs/build/

# Storybook build outputs
storybook-static/

# ===== SUPABASE =====
# Supabase local development
.supabase/

# ===== TYPESCRIPT =====
# TypeScript cache
*.tsbuildinfo

# ===== TAILWIND CSS =====
# Tailwind CSS build files (if any)
tailwind.output.css
